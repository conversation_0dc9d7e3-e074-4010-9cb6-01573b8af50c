failures:

---- integration::executor_tests::test_priority_queue_routing stdout ----


=== STARTING DYNAMIC PRIORITY QUEUE ROUTING TEST ===
Current thread: ThreadId(23)
Creating TaskExecutor with dynamic decision making
Initializing TaskExecutor...
Starting Rayon queue...
Starting Rayon queue with current status: Stopped
Starting Rayon queue
Creating new channel with capacity 10000
New channel created
Updating queue status to Running
Queue status updated to Running
Creating worker loop with dedicated pool: false
Worker loop created
Storing worker handle
Worker handle stored
Waiting for worker loop to initialize
Rayon worker loop started
Creating semaphore with 16 max concurrent tasks
Rayon worker loop waiting for tasks
Initial queue status in worker loop: Running
Starting to receive tasks from channel
Waiting for task with 1 second timeout
Rayon worker loop ping - still alive
Worker loop initialization wait complete
Rayon queue started
Rayon queue started successfully
Setting RayonQueue reference in BackgroundPriorityQueue...
RayonQueue reference set in BackgroundPriorityQueue
Starting priority queues...
Starting Background queue with current status: Stopped
Starting Background queue
Creating custom worker loop that transfers tasks to the RayonQueue
Creating new channel with capacity 10000
New channel created
Custom worker loop created
Storing worker handle
Worker handle stored
Updating queue status to Running
Queue status updated to Running
Waiting for worker loop to initialize
Background queue worker loop started
Background queue worker loop ping
Worker loop initialization wait complete
Background queue started
Starting Standard queue with current status: Stopped
Starting Standard queue
Creating new channel with capacity 5000
New channel created
Creating worker loop
Storing worker handle
Worker handle stored
Updating queue status to Running
Queue status updated to Running
Waiting for worker loop to initialize
Priority queue worker loop started for strategy: Tokio
Priority queue worker loop ping - strategy: Tokio
Worker loop initialization wait complete
Standard queue started
Starting RealTime queue with current status: Stopped
Starting RealTime queue
Creating new channel with capacity 1100
New channel created
Creating worker loop
Storing worker handle
Worker handle stored
Updating queue status to Running
Queue status updated to Running
Waiting for worker loop to initialize
Priority queue worker loop started for strategy: Tokio
Priority queue worker loop ping - strategy: Tokio
Worker loop initialization wait complete
RealTime queue started
Priority queues started successfully
  Rayon queue status: Running
Starting Tokio queue...
Starting Tokio queue
Updating queue status to Running
Queue status updated to Running
Creating new channel with capacity 10000
Creating worker loop
Tokio queue started
  Tokio queue status: Running
Tokio queue started successfully
Setting up queue monitoring with interval of 60 seconds
TaskExecutor initialized with 6 monitor tasks
Queue status after initialization:
  Background queue: Running
  Standard queue: Running
  RealTime queue: Running
  Rayon queue: Running
  Tokio queue: Running
Executor with decision maker initialized successfully
Executor configuration:
  - Queue statuses:
    - Background queue: Running
    - Standard queue: Running
    - RealTime queue: Running
    - Rayon queue: Running
    - Tokio queue: Running

Creating dynamic test tasks with different priorities and characteristics
Creating simple Realtime priority task...
Created simple Realtime priority task
Creating complex High priority task...
Created complex High priority task
Creating Normal priority task...
Created Normal priority task
Creating CPU-intensive Low priority task...
Created CPU-intensive Low priority task

Submitting tasks to executor for dynamic routing...
Submitting simple Realtime priority task (should be routed to realtime queue with dynamic strategy)
Executor received task 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2 with priority Realtime, category UICallback for strategy Direct
Dynamic strategy selected: Tokio for task 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2 (category: UICallback, priority: Realtime)
Using priority-based routing for task 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2 with priority Realtime
Note: Provided strategy Direct will be used as a hint but priority routing takes precedence
Routing to RealTime queue with strategy Tokio (dynamic: true)
Priority queue will use strategy Tokio for task 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2
Enqueueing task 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2 in RealTime queue
Task 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2 queued successfully in priority queue
Realtime priority task submitted with ID: 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2
Submitting complex High priority task (should be routed to realtime queue with dynamic strategy)
Executor received task 9bac006e-474f-46d2-8b5b-154b162dc11f with priority High, category LLMInference for strategy Direct
Dynamic strategy selected: Tokio for task 9bac006e-474f-46d2-8b5b-154b162dc11f (category: LLMInference, priority: High)
Using priority-based routing for task 9bac006e-474f-46d2-8b5b-154b162dc11f with priority High
Note: Provided strategy Direct will be used as a hint but priority routing takes precedence
Routing to RealTime queue with strategy Tokio (dynamic: true)
Priority queue will use strategy Tokio for task 9bac006e-474f-46d2-8b5b-154b162dc11f
Enqueueing task 9bac006e-474f-46d2-8b5b-154b162dc11f in RealTime queue
Task 9bac006e-474f-46d2-8b5b-154b162dc11f queued successfully in priority queue
High priority task submitted with ID: 9bac006e-474f-46d2-8b5b-154b162dc11f
Submitting Normal priority task (should be routed to standard queue with dynamic strategy)
Executor received task d26d62ac-6e4d-4e77-ba66-7561404ff5c0 with priority Normal, category DatabaseQuery for strategy Direct
Dynamic strategy selected: Tokio for task d26d62ac-6e4d-4e77-ba66-7561404ff5c0 (category: DatabaseQuery, priority: Normal)
Using priority-based routing for task d26d62ac-6e4d-4e77-ba66-7561404ff5c0 with priority Normal
Note: Provided strategy Direct will be used as a hint but priority routing takes precedence
Routing to Standard queue with strategy Tokio (dynamic: true)
Priority queue will use strategy Tokio for task d26d62ac-6e4d-4e77-ba66-7561404ff5c0
Enqueueing task d26d62ac-6e4d-4e77-ba66-7561404ff5c0 in Standard queue
Task d26d62ac-6e4d-4e77-ba66-7561404ff5c0 queued successfully in priority queue
Normal priority task submitted with ID: d26d62ac-6e4d-4e77-ba66-7561404ff5c0
Submitting Low priority task (should be routed to background queue)
Executor received task 390aa26e-b9b3-4576-a575-a646f69305e1 with priority Low, category FileProcessing for strategy Direct
Dynamic strategy selected: Rayon for task 390aa26e-b9b3-4576-a575-a646f69305e1 (category: FileProcessing, priority: Low)
Using priority-based routing for task 390aa26e-b9b3-4576-a575-a646f69305e1 with priority Low
Note: Provided strategy Direct will be used as a hint but priority routing takes precedence
Routing to Background queue with strategy Rayon (dynamic: true)
Background queue status: Running
Priority queue will use strategy Rayon for task 390aa26e-b9b3-4576-a575-a646f69305e1
Enqueueing task 390aa26e-b9b3-4576-a575-a646f69305e1 in Background queue
Background queue status before enqueue: Running
Background queue enqueue called with status: Running
Enqueuing task 390aa26e-b9b3-4576-a575-a646f69305e1 in Background queue
Created oneshot channel for task 390aa26e-b9b3-4576-a575-a646f69305e1
Created PrioritizedTask for task 390aa26e-b9b3-4576-a575-a646f69305e1
Sending task 390aa26e-b9b3-4576-a575-a646f69305e1 to Background queue channel
Successfully sent task 390aa26e-b9b3-4576-a575-a646f69305e1 to Background queue channel
Updating Background queue length from 0 to 1
Task 390aa26e-b9b3-4576-a575-a646f69305e1 enqueued successfully in Background queue
Background queue enqueue result: true
Task 390aa26e-b9b3-4576-a575-a646f69305e1 queued successfully in priority queue
Low priority task submitted with ID: 390aa26e-b9b3-4576-a575-a646f69305e1

Queue statuses after submission:
  - Background queue: Running
  - Standard queue: Running
  - RealTime queue: Running
  - Rayon queue: Running
  - Tokio queue: Running

Waiting for task results with timeout (60 seconds)
Waiting for Realtime priority task result...
Tokio worker loop started
Initial queue status in worker loop: Running
Starting to receive tasks from channel
Priority queue worker received task 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2 with priority Realtime for strategy Tokio
Updating queue length from 2 to 1
Updating active tasks count from 0 to 1
Starting task execution with strategy: Tokio for task 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2
Executing task 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2 with Tokio strategy
DynamicRoutingTask::execute called for task 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2 (Simple Realtime Task)
Task characteristics: complex=false, cpu_intensive=false, category=UICallback, priority=Realtime
Executing on thread: integration::executor_tests::test_priority_queue_routing
Detected execution strategy: Direct
Simulating simple work for task 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2
Priority queue worker received task d26d62ac-6e4d-4e77-ba66-7561404ff5c0 with priority Normal for strategy Tokio
Updating queue length from 1 to 0
Updating active tasks count from 0 to 1
Starting task execution with strategy: Tokio for task d26d62ac-6e4d-4e77-ba66-7561404ff5c0
Executing task d26d62ac-6e4d-4e77-ba66-7561404ff5c0 with Tokio strategy
DynamicRoutingTask::execute called for task d26d62ac-6e4d-4e77-ba66-7561404ff5c0 (Normal Priority Task)
Task characteristics: complex=false, cpu_intensive=false, category=DatabaseQuery, priority=Normal
Executing on thread: integration::executor_tests::test_priority_queue_routing
Detected execution strategy: Direct
Simulating simple work for task d26d62ac-6e4d-4e77-ba66-7561404ff5c0
Background queue worker received task 390aa26e-b9b3-4576-a575-a646f69305e1
Updating queue length from 1 to 0
Transferring task 390aa26e-b9b3-4576-a575-a646f69305e1 to RayonQueue
RayonQueue status before enqueuing task 390aa26e-b9b3-4576-a575-a646f69305e1: Running
Enqueue called with queue status: Running
Enqueuing task 390aa26e-b9b3-4576-a575-a646f69305e1 in Rayon queue
Created oneshot channel for task 390aa26e-b9b3-4576-a575-a646f69305e1
Created RayonTask for task 390aa26e-b9b3-4576-a575-a646f69305e1
Sending task 390aa26e-b9b3-4576-a575-a646f69305e1 to channel
Successfully sent task 390aa26e-b9b3-4576-a575-a646f69305e1 to channel
Updating queue length from 0 to 1
Task 390aa26e-b9b3-4576-a575-a646f69305e1 enqueued successfully in Rayon queue
Task 390aa26e-b9b3-4576-a575-a646f69305e1 successfully transferred to RayonQueue
Rayon worker received a task
Current queue status: Running
Rayon worker processing task 390aa26e-b9b3-4576-a575-a646f69305e1
Updating active tasks count from 0 to 1
Updating queue length from 1 to 0
Acquiring semaphore permit for task 390aa26e-b9b3-4576-a575-a646f69305e1
Successfully acquired semaphore permit for task 390aa26e-b9b3-4576-a575-a646f69305e1
Preparing to execute task 390aa26e-b9b3-4576-a575-a646f69305e1
Acquiring lock on task 390aa26e-b9b3-4576-a575-a646f69305e1
Lock acquired for task 390aa26e-b9b3-4576-a575-a646f69305e1
Using global pool for task 390aa26e-b9b3-4576-a575-a646f69305e1
Using timeout of 30s for task 390aa26e-b9b3-4576-a575-a646f69305e1
Starting task execution with Rayon for task 390aa26e-b9b3-4576-a575-a646f69305e1
Using global Rayon pool for task 390aa26e-b9b3-4576-a575-a646f69305e1
Task cloned for global Rayon execution: 390aa26e-b9b3-4576-a575-a646f69305e1
Spawning task 390aa26e-b9b3-4576-a575-a646f69305e1 on global Rayon thread pool
Waiting for result from global Rayon thread pool for task 390aa26e-b9b3-4576-a575-a646f69305e1
Tokio worker loop ping - still alive
DynamicRoutingTask::execute completed for task 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2 with strategy Direct
Task 1e04844e-7968-41f2-8ccb-2e06ffc1c1c2 execution completed in 12.431754ms with result: true
Priority queue worker received task 9bac006e-474f-46d2-8b5b-154b162dc11f with priority High for strategy Tokio
Updating queue length from 1 to 0
Updating active tasks count from 0 to 1
Starting task execution with strategy: Tokio for task 9bac006e-474f-46d2-8b5b-154b162dc11f
Executing task 9bac006e-474f-46d2-8b5b-154b162dc11f with Tokio strategy
DynamicRoutingTask::execute called for task 9bac006e-474f-46d2-8b5b-154b162dc11f (Complex High Priority Task)
Task characteristics: complex=true, cpu_intensive=false, category=LLMInference, priority=High
Executing on thread: integration::executor_tests::test_priority_queue_routing
Detected execution strategy: Direct
Simulating complex work for task 9bac006e-474f-46d2-8b5b-154b162dc11f
DynamicRoutingTask::execute completed for task d26d62ac-6e4d-4e77-ba66-7561404ff5c0 with strategy Direct
Task d26d62ac-6e4d-4e77-ba66-7561404ff5c0 execution completed in 12.424718ms with result: true
Realtime priority task completed successfully
Waiting for High priority task result...
Successfully received result for task 390aa26e-b9b3-4576-a575-a646f69305e1 from global pool
Task 390aa26e-b9b3-4576-a575-a646f69305e1 execution succeeded with Rayon in 12 ms
Task 390aa26e-b9b3-4576-a575-a646f69305e1 completed within timeout
Updating statistics for task 390aa26e-b9b3-4576-a575-a646f69305e1
Updating active tasks count from 1 to 0
Sending result for task 390aa26e-b9b3-4576-a575-a646f69305e1
Successfully sent result for task 390aa26e-b9b3-4576-a575-a646f69305e1
Task 390aa26e-b9b3-4576-a575-a646f69305e1 processing completed
Waiting for task with 1 second timeout
Received result from RayonQueue for task 390aa26e-b9b3-4576-a575-a646f69305e1
Successfully forwarded result for task 390aa26e-b9b3-4576-a575-a646f69305e1
DynamicRoutingTask::execute completed for task 9bac006e-474f-46d2-8b5b-154b162dc11f with strategy Direct
Task 9bac006e-474f-46d2-8b5b-154b162dc11f execution completed in 52.492102ms with result: true
High priority task completed successfully
Waiting for Normal priority task result...
Normal priority task completed successfully
Waiting for Low priority task result...
Low priority task completed successfully

Extracting dynamic task results...
Dynamic task execution results:
  Realtime task: Simple Realtime Task executed with strategy: Direct (simple: true, cpu_intensive: true)
  High task: Complex High Priority Task executed with strategy: Direct (complex: true, cpu_intensive: false)
  Normal task: Normal Priority Task executed with strategy: Direct (simple: true, cpu_intensive: true)
  Low task: CPU-intensive Low Priority Task executed with strategy: Rayon (complex: false, cpu_intensive: true)

Running dynamic routing assertions...
Asserting simple Realtime priority task used appropriate strategy for speed
Asserting complex High priority task used Tokio strategy for complexity

thread 'integration::executor_tests::test_priority_queue_routing' panicked at prisma_ai/tests/integration/executor_tests.rs:1648:5:
assertion `left == right` failed: Complex high priority task should use Tokio strategy for handling complexity, got: Direct
  left: Direct
 right: Tokio
note: run with `RUST_BACKTRACE=1` environment variable to display a backtrace


failures:
    integration::executor_tests::test_priority_queue_routing

test result: FAILED. 27 passed; 1 failed; 0 ignored; 0 measured; 127 filtered out; finished in 3.81s