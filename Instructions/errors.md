running 1 test


=== STARTING DYNAMIC PRIORITY QUEUE ROUTING TEST ===
Current thread: ThreadId(2)
Creating TaskExecutor with dynamic decision making
Initializing TaskExecutor...
Starting Rayon queue...
Starting Rayon queue with current status: Stopped
Starting Rayon queue
Creating new channel with capacity 10000
New channel created
Updating queue status to Running
Queue status updated to Running
Creating worker loop with dedicated pool: false
Worker loop created
Storing worker handle
Worker handle stored
Waiting for worker loop to initialize
Rayon worker loop started
Creating semaphore with 16 max concurrent tasks
Rayon worker loop waiting for tasks
Initial queue status in worker loop: Running
Starting to receive tasks from channel
Waiting for task with 1 second timeout
Rayon worker loop ping - still alive
Worker loop initialization wait complete
Rayon queue started
Rayon queue started successfully
Setting RayonQueue reference in BackgroundPriorityQueue...
RayonQueue reference set in BackgroundPriorityQueue
Starting priority queues...
Starting Background queue with current status: Stopped
Starting Background queue
Creating custom worker loop that transfers tasks to the RayonQueue
Creating new channel with capacity 10000
New channel created
Custom worker loop created
Storing worker handle
Worker handle stored
Updating queue status to Running
Queue status updated to Running
Waiting for worker loop to initialize
Background queue worker loop started
Background queue worker loop ping
Worker loop initialization wait complete
Background queue started
Starting Standard queue with current status: Stopped
Starting Standard queue
Creating new channel with capacity 5000
New channel created
Creating worker loop
Storing worker handle
Worker handle stored
Updating queue status to Running
Queue status updated to Running
Waiting for worker loop to initialize
Priority queue worker loop started for strategy: Tokio
Priority queue worker loop ping - strategy: Tokio
Worker loop initialization wait complete
Standard queue started
Starting RealTime queue with current status: Stopped
Starting RealTime queue
Creating new channel with capacity 1100
New channel created
Creating worker loop
Storing worker handle
Worker handle stored
Updating queue status to Running
Queue status updated to Running
Waiting for worker loop to initialize
Priority queue worker loop started for strategy: Tokio
Priority queue worker loop ping - strategy: Tokio
Worker loop initialization wait complete
RealTime queue started
Priority queues started successfully
  Rayon queue status: Running
Starting Tokio queue...
Starting Tokio queue
Updating queue status to Running
Queue status updated to Running
Creating new channel with capacity 10000
Creating worker loop
Tokio queue started
  Tokio queue status: Running
Tokio queue started successfully
Setting up queue monitoring with interval of 60 seconds
TaskExecutor initialized with 6 monitor tasks
Queue status after initialization:
  Background queue: Running
  Standard queue: Running
  RealTime queue: Running
  Rayon queue: Running
  Tokio queue: Running
Executor with decision maker initialized successfully
Executor configuration:
  - Queue statuses:
    - Background queue: Running
    - Standard queue: Running
    - RealTime queue: Running
    - Rayon queue: Running
    - Tokio queue: Running

Creating dynamic test tasks with different priorities and characteristics
Creating simple Realtime priority task...
Created simple Realtime priority task
Creating complex High priority task...
Created complex High priority task
Creating Normal priority task...
Created Normal priority task
Creating CPU-intensive Low priority task...
Created CPU-intensive Low priority task

Submitting tasks to executor for dynamic routing...
Submitting simple Realtime priority task (should be routed to realtime queue with dynamic strategy)
Executor received task b14fe447-99e2-4c42-ac62-d91a0ccbaca3 with priority Realtime, category UICallback for strategy Direct
Dynamic strategy selected: Tokio for task b14fe447-99e2-4c42-ac62-d91a0ccbaca3 (category: UICallback, priority: Realtime)
Using priority-based routing for task b14fe447-99e2-4c42-ac62-d91a0ccbaca3 with priority Realtime
Note: Provided strategy Direct will be used as a hint but priority routing takes precedence
Routing to RealTime queue with strategy Tokio (dynamic: true)
Priority queue will use strategy Tokio for task b14fe447-99e2-4c42-ac62-d91a0ccbaca3
Enqueueing task b14fe447-99e2-4c42-ac62-d91a0ccbaca3 in RealTime queue
Task b14fe447-99e2-4c42-ac62-d91a0ccbaca3 queued successfully in priority queue
Realtime priority task submitted with ID: b14fe447-99e2-4c42-ac62-d91a0ccbaca3
Submitting complex High priority task (should be routed to realtime queue with dynamic strategy)
Executor received task 72b5d5b4-8571-4d8d-a51e-018561c2cfcd with priority High, category LLMInference for strategy Direct
Dynamic strategy selected: Tokio for task 72b5d5b4-8571-4d8d-a51e-018561c2cfcd (category: LLMInference, priority: High)
Using priority-based routing for task 72b5d5b4-8571-4d8d-a51e-018561c2cfcd with priority High
Note: Provided strategy Direct will be used as a hint but priority routing takes precedence
Routing to RealTime queue with strategy Tokio (dynamic: true)
Priority queue will use strategy Tokio for task 72b5d5b4-8571-4d8d-a51e-018561c2cfcd
Enqueueing task 72b5d5b4-8571-4d8d-a51e-018561c2cfcd in RealTime queue
Task 72b5d5b4-8571-4d8d-a51e-018561c2cfcd queued successfully in priority queue
High priority task submitted with ID: 72b5d5b4-8571-4d8d-a51e-018561c2cfcd
Submitting Normal priority task (should be routed to standard queue with dynamic strategy)
Executor received task 12038e58-2c74-4cf2-9510-ec73a8cddc58 with priority Normal, category DatabaseQuery for strategy Direct
Dynamic strategy selected: Tokio for task 12038e58-2c74-4cf2-9510-ec73a8cddc58 (category: DatabaseQuery, priority: Normal)
Using priority-based routing for task 12038e58-2c74-4cf2-9510-ec73a8cddc58 with priority Normal
Note: Provided strategy Direct will be used as a hint but priority routing takes precedence
Routing to Standard queue with strategy Tokio (dynamic: true)
Priority queue will use strategy Tokio for task 12038e58-2c74-4cf2-9510-ec73a8cddc58
Enqueueing task 12038e58-2c74-4cf2-9510-ec73a8cddc58 in Standard queue
Task 12038e58-2c74-4cf2-9510-ec73a8cddc58 queued successfully in priority queue
Normal priority task submitted with ID: 12038e58-2c74-4cf2-9510-ec73a8cddc58
Submitting Low priority task (should be routed to background queue)
Executor received task af67e6bc-a87d-4966-a6c8-8b99858ab645 with priority Low, category FileProcessing for strategy Direct
Dynamic strategy selected: Rayon for task af67e6bc-a87d-4966-a6c8-8b99858ab645 (category: FileProcessing, priority: Low)
Using priority-based routing for task af67e6bc-a87d-4966-a6c8-8b99858ab645 with priority Low
Note: Provided strategy Direct will be used as a hint but priority routing takes precedence
Routing to Background queue with strategy Rayon (dynamic: true)
Background queue status: Running
Priority queue will use strategy Rayon for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Enqueueing task af67e6bc-a87d-4966-a6c8-8b99858ab645 in Background queue
Background queue status before enqueue: Running
Background queue enqueue called with status: Running
Enqueuing task af67e6bc-a87d-4966-a6c8-8b99858ab645 in Background queue
Created oneshot channel for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Created PrioritizedTask for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Sending task af67e6bc-a87d-4966-a6c8-8b99858ab645 to Background queue channel
Successfully sent task af67e6bc-a87d-4966-a6c8-8b99858ab645 to Background queue channel
Updating Background queue length from 0 to 1
Task af67e6bc-a87d-4966-a6c8-8b99858ab645 enqueued successfully in Background queue
Background queue enqueue result: true
Task af67e6bc-a87d-4966-a6c8-8b99858ab645 queued successfully in priority queue
Low priority task submitted with ID: af67e6bc-a87d-4966-a6c8-8b99858ab645

Queue statuses after submission:
  - Background queue: Running
  - Standard queue: Running
  - RealTime queue: Running
  - Rayon queue: Running
  - Tokio queue: Running

Waiting for task results with timeout (60 seconds)
Waiting for Realtime priority task result...
Tokio worker loop started
Initial queue status in worker loop: Running
Starting to receive tasks from channel
Priority queue worker received task b14fe447-99e2-4c42-ac62-d91a0ccbaca3 with priority Realtime for strategy Tokio
Updating queue length from 2 to 1
Updating active tasks count from 0 to 1
Starting task execution with strategy: Tokio for task b14fe447-99e2-4c42-ac62-d91a0ccbaca3
Executing task b14fe447-99e2-4c42-ac62-d91a0ccbaca3 with Tokio strategy
DynamicRoutingTask::execute called for task b14fe447-99e2-4c42-ac62-d91a0ccbaca3 (Simple Realtime Task)
Task characteristics: complex=false, cpu_intensive=false, category=UICallback, priority=Realtime
Executing on thread: integration::executor_tests::test_priority_queue_routing
Detected execution strategy: Tokio
Simulating simple work for task b14fe447-99e2-4c42-ac62-d91a0ccbaca3
Priority queue worker received task 12038e58-2c74-4cf2-9510-ec73a8cddc58 with priority Normal for strategy Tokio
Updating queue length from 1 to 0
Updating active tasks count from 0 to 1
Starting task execution with strategy: Tokio for task 12038e58-2c74-4cf2-9510-ec73a8cddc58
Executing task 12038e58-2c74-4cf2-9510-ec73a8cddc58 with Tokio strategy
DynamicRoutingTask::execute called for task 12038e58-2c74-4cf2-9510-ec73a8cddc58 (Normal Priority Task)
Task characteristics: complex=false, cpu_intensive=false, category=DatabaseQuery, priority=Normal
Executing on thread: integration::executor_tests::test_priority_queue_routing
Detected execution strategy: Tokio
Simulating simple work for task 12038e58-2c74-4cf2-9510-ec73a8cddc58
Background queue worker received task af67e6bc-a87d-4966-a6c8-8b99858ab645
Updating queue length from 1 to 0
Transferring task af67e6bc-a87d-4966-a6c8-8b99858ab645 to RayonQueue
RayonQueue status before enqueuing task af67e6bc-a87d-4966-a6c8-8b99858ab645: Running
Enqueue called with queue status: Running
Enqueuing task af67e6bc-a87d-4966-a6c8-8b99858ab645 in Rayon queue
Created oneshot channel for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Created RayonTask for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Sending task af67e6bc-a87d-4966-a6c8-8b99858ab645 to channel
Successfully sent task af67e6bc-a87d-4966-a6c8-8b99858ab645 to channel
Updating queue length from 0 to 1
Task af67e6bc-a87d-4966-a6c8-8b99858ab645 enqueued successfully in Rayon queue
Task af67e6bc-a87d-4966-a6c8-8b99858ab645 successfully transferred to RayonQueue
Rayon worker received a task
Current queue status: Running
Rayon worker processing task af67e6bc-a87d-4966-a6c8-8b99858ab645
Updating active tasks count from 0 to 1
Updating queue length from 1 to 0
Acquiring semaphore permit for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Successfully acquired semaphore permit for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Preparing to execute task af67e6bc-a87d-4966-a6c8-8b99858ab645
Acquiring lock on task af67e6bc-a87d-4966-a6c8-8b99858ab645
Lock acquired for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Using global pool for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Using timeout of 30s for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Starting task execution with Rayon for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Using global Rayon pool for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Task cloned for global Rayon execution: af67e6bc-a87d-4966-a6c8-8b99858ab645
Spawning task af67e6bc-a87d-4966-a6c8-8b99858ab645 on global Rayon thread pool
Waiting for result from global Rayon thread pool for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Inside global Rayon thread pool for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Creating new Tokio runtime for task af67e6bc-a87d-4966-a6c8-8b99858ab645 in global pool
Executing task af67e6bc-a87d-4966-a6c8-8b99858ab645 on new runtime in global pool
DynamicRoutingTask::execute called for task af67e6bc-a87d-4966-a6c8-8b99858ab645 (CPU-intensive Low Priority Task)
Task characteristics: complex=false, cpu_intensive=true, category=FileProcessing, priority=Low
Executing on thread: unnamed
Detected execution strategy: Tokio
Simulating simple work for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Tokio worker loop ping - still alive
DynamicRoutingTask::execute completed for task b14fe447-99e2-4c42-ac62-d91a0ccbaca3 with strategy Tokio
Task b14fe447-99e2-4c42-ac62-d91a0ccbaca3 execution completed in 11.497138ms with result: true
Priority queue worker received task 72b5d5b4-8571-4d8d-a51e-018561c2cfcd with priority High for strategy Tokio
Updating queue length from 1 to 0
Updating active tasks count from 0 to 1
Starting task execution with strategy: Tokio for task 72b5d5b4-8571-4d8d-a51e-018561c2cfcd
Executing task 72b5d5b4-8571-4d8d-a51e-018561c2cfcd with Tokio strategy
DynamicRoutingTask::execute called for task 72b5d5b4-8571-4d8d-a51e-018561c2cfcd (Complex High Priority Task)
Task characteristics: complex=true, cpu_intensive=false, category=LLMInference, priority=High
Executing on thread: integration::executor_tests::test_priority_queue_routing
Detected execution strategy: Tokio
Simulating complex work for task 72b5d5b4-8571-4d8d-a51e-018561c2cfcd
DynamicRoutingTask::execute completed for task 12038e58-2c74-4cf2-9510-ec73a8cddc58 with strategy Tokio
Task 12038e58-2c74-4cf2-9510-ec73a8cddc58 execution completed in 11.471915ms with result: true
Realtime priority task completed successfully
Waiting for High priority task result...
Simulating CPU-intensive work for task af67e6bc-a87d-4966-a6c8-8b99858ab645
CPU-intensive work completed for task af67e6bc-a87d-4966-a6c8-8b99858ab645
DynamicRoutingTask::execute completed for task af67e6bc-a87d-4966-a6c8-8b99858ab645 with strategy Tokio
Task af67e6bc-a87d-4966-a6c8-8b99858ab645 execution completed with result: true
Sending result for task af67e6bc-a87d-4966-a6c8-8b99858ab645 back to main thread from global pool
Successfully sent result for task af67e6bc-a87d-4966-a6c8-8b99858ab645 from global pool
Successfully received result for task af67e6bc-a87d-4966-a6c8-8b99858ab645 from global pool
Task af67e6bc-a87d-4966-a6c8-8b99858ab645 execution succeeded with Rayon in 12 ms
Task af67e6bc-a87d-4966-a6c8-8b99858ab645 completed within timeout
Updating statistics for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Updating active tasks count from 1 to 0
Sending result for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Successfully sent result for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Task af67e6bc-a87d-4966-a6c8-8b99858ab645 processing completed
Waiting for task with 1 second timeout
Received result from RayonQueue for task af67e6bc-a87d-4966-a6c8-8b99858ab645
Successfully forwarded result for task af67e6bc-a87d-4966-a6c8-8b99858ab645
DynamicRoutingTask::execute completed for task 72b5d5b4-8571-4d8d-a51e-018561c2cfcd with strategy Tokio
Task 72b5d5b4-8571-4d8d-a51e-018561c2cfcd execution completed in 52.326615ms with result: true
High priority task completed successfully
Waiting for Normal priority task result...
Normal priority task completed successfully
Waiting for Low priority task result...
Low priority task completed successfully

Extracting dynamic task results...
Dynamic task execution results:
  Realtime task: Simple Realtime Task executed with strategy: Tokio (simple: true, cpu_intensive: true)
  High task: Complex High Priority Task executed with strategy: Tokio (complex: true, cpu_intensive: false)
  Normal task: Normal Priority Task executed with strategy: Tokio (simple: true, cpu_intensive: true)
  Low task: CPU-intensive Low Priority Task executed with strategy: Tokio (complex: false, cpu_intensive: true)

Running dynamic routing assertions...
Verifying task characteristics were preserved:
Verifying priority queue routing:
Verifying task categories:
Verifying CPU-intensive task used Rayon strategy:

thread 'integration::executor_tests::test_priority_queue_routing' panicked at prisma_ai/tests/integration/executor_tests.rs:1682:5:
assertion `left == right` failed: CPU-intensive low priority task should use Rayon strategy for parallelization, got: Tokio
  left: Tokio
 right: Rayon
note: run with `RUST_BACKTRACE=1` environment variable to display a backtrace
test integration::executor_tests::test_priority_queue_routing ... FAILED

failures:

failures:
    integration::executor_tests::test_priority_queue_routing

test result: FAILED. 0 passed; 1 failed; 0 ignored; 0 measured; 154 filtered out; finished in 0.47s