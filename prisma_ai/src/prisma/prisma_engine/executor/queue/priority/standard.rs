// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/priority/standard.rs
// =================================================================================================
// Purpose: Implements the StandardPriorityQueue for handling normal-priority tasks. This queue is
// designed for tasks that require timely execution but are not critical.
//
// Integration:
// - `traits.rs`: Implements the PriorityQueueTrait and StandardQueueTrait defined there.
// - `types.rs`: Uses the types defined there.
// - `generics.rs`: Uses the utility functions defined there.
// - `Task`: Operates on Task objects.
// - `err.rs`: Uses PrismaResult for error handling.
// =================================================================================================

use async_trait::async_trait;
use std::any::Any;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::{mpsc, Mutex, oneshot};
use tokio::task::JoinHandle;
use tracing::{debug, error, info, instrument, warn};

use crate::err::{GenericError, PrismaResult};
use crate::prisma::prisma_engine::executor::queue::direct::CloneableTask;
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::{ExecutionStrategyType, TaskId, TaskPriority};

use super::generics::create_worker_loop;
use super::traits::{CloneableTaskWrapper, PriorityQueueTrait, StandardQueueTrait};
use super::types::{PrioritizedTask, PriorityQueueConfig, PriorityQueueStats, QueueStatus};

/// A queue for handling normal-priority standard tasks.
///
/// This queue is designed for tasks that require timely execution but are not critical.
/// It includes timeout functionality to prevent tasks from running too long.
#[derive(Debug)]
pub struct StandardPriorityQueue {
    /// Configuration for the queue
    config: PriorityQueueConfig,

    /// Channel sender for enqueueing tasks
    tx: mpsc::Sender<PrioritizedTask>,

    /// Channel receiver for dequeueing tasks
    rx: Option<mpsc::Receiver<PrioritizedTask>>,

    /// Timeout for task execution
    task_timeout: Duration,

    /// Statistics about task execution
    stats: Arc<RwLock<PriorityQueueStats>>,

    /// Status of the queue
    status: Arc<RwLock<QueueStatus>>,

    /// Handles for the worker tasks
    worker_handles: Vec<JoinHandle<()>>,
}

impl Clone for StandardPriorityQueue {
    fn clone(&self) -> Self {
        // Create a new channel with the same capacity
        let (tx, rx) = mpsc::channel(self.config.standard_queue_capacity);

        StandardPriorityQueue {
            config: self.config.clone(),
            tx,
            rx: Some(rx),
            task_timeout: self.task_timeout,
            stats: self.stats.clone(),
            status: self.status.clone(),
            worker_handles: Vec::new(), // Empty vector for worker handles
        }
    }
}

impl StandardPriorityQueue {
    /// Creates a new StandardPriorityQueue with the given configuration.
    pub fn new(config: PriorityQueueConfig) -> Self {
        let (tx, rx) = mpsc::channel(config.standard_queue_capacity);

        StandardPriorityQueue {
            task_timeout: Duration::from_millis(config.standard_task_timeout_ms),
            stats: Arc::new(RwLock::new(PriorityQueueStats::new())),
            status: Arc::new(RwLock::new(QueueStatus::Stopped)),
            worker_handles: Vec::new(),
            config,
            tx,
            rx: Some(rx),
        }
    }

    /// Creates a new StandardPriorityQueue with default configuration.
    pub fn default() -> Self {
        Self::new(PriorityQueueConfig::default())
    }

    /// Returns a clone of the current statistics.
    pub fn get_stats(&self) -> PriorityQueueStats {
        self.stats.read().unwrap().clone()
    }

    /// Returns the current status of the queue.
    pub fn get_status(&self) -> QueueStatus {
        *self.status.read().unwrap()
    }
}

#[async_trait]
impl PriorityQueueTrait for StandardPriorityQueue {
    fn priority_level(&self) -> Vec<TaskPriority> {
        vec![TaskPriority::Normal]
    }

    fn execution_strategy(&self) -> ExecutionStrategyType {
        self.config.standard_strategy
    }

    #[instrument(
        name = "standard_queue_enqueue",
        skip(self, task),
        level = "debug"
    )]
    async fn enqueue(
        &self,
        task: Box<dyn Task>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
    )> {
        // Check if the queue is running
        if *self.status.read().unwrap() != QueueStatus::Running {
            return Err(GenericError::from(
                "Cannot enqueue task: Standard queue is not running",
            ));
        }

        let task_id = task.id();
        let priority = task.priority();

        // Verify that the task has the correct priority
        if priority != TaskPriority::Normal {
            warn!(
                "Task {} has priority {:?} but was submitted to Standard queue",
                task_id, priority
            );
        }

        debug!("Enqueuing task {} in Standard queue", task_id);

        // Create a oneshot channel to receive the task result
        let (result_sender, result_receiver) = oneshot::channel();

        // Create a PrioritizedTask
        let prioritized_task = PrioritizedTask {
            priority,
            task_id,
            task: Arc::new(Mutex::new(task)),
            result_sender: Some(result_sender),
            enqueued_at: Instant::now(),
        };

        // Send the task to the channel
        if let Err(e) = self.tx.send(prioritized_task).await {
            let err_msg = format!("Failed to enqueue task {}: {}", task_id, e);
            error!("{}", err_msg);
            return Err(GenericError::from(err_msg));
        }

        // Update queue length in stats
        {
            let mut stats = self.stats.write().unwrap();
            let current_length = stats.queue_length;
            stats.update_queue_length(current_length + 1);
        }

        debug!("Task {} enqueued successfully in Standard queue", task_id);
        Ok((task_id, result_receiver))
    }

    #[instrument(
        name = "standard_queue_enqueue_arc",
        skip(self, task),
        level = "debug"
    )]
    async fn enqueue_arc<T: CloneableTask + 'static>(
        &self,
        task: Arc<T>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
    )> {
        // Check if the queue is running
        if *self.status.read().unwrap() != QueueStatus::Running {
            return Err(GenericError::from(
                "Cannot enqueue task: Standard queue is not running",
            ));
        }

        let task_id = task.id();
        let priority = task.priority();

        // Verify that the task has the correct priority
        if priority != TaskPriority::Normal {
            warn!(
                "Task {} has priority {:?} but was submitted to Standard queue",
                task_id, priority
            );
        }

        debug!("Enqueuing task {} in Standard queue (Arc)", task_id);

        // Create a oneshot channel to receive the task result
        let (result_sender, result_receiver) = oneshot::channel();

        // Create a PrioritizedTask with the Arc<T> directly
        // We'll handle the task execution in the worker loop
        let prioritized_task = PrioritizedTask {
            priority,
            task_id,
            task: Arc::new(Mutex::new(Box::new(CloneableTaskWrapper(task)))),
            result_sender: Some(result_sender),
            enqueued_at: Instant::now(),
        };

        // Send the task to the channel
        if let Err(e) = self.tx.send(prioritized_task).await {
            let err_msg = format!("Failed to enqueue task {}: {}", task_id, e);
            error!("{}", err_msg);
            return Err(GenericError::from(err_msg));
        }

        // Update queue length in stats
        {
            let mut stats = self.stats.write().unwrap();
            let current_length = stats.queue_length;
            stats.update_queue_length(current_length + 1);
        }

        debug!("Task {} enqueued successfully in Standard queue (Arc)", task_id);
        Ok((task_id, result_receiver))
    }

    fn queue_length(&self) -> usize {
        self.stats.read().unwrap().queue_length
    }

    fn is_full(&self) -> bool {
        self.queue_length() >= self.capacity()
    }

    fn capacity(&self) -> usize {
        self.config.standard_queue_capacity
    }

    async fn start(&mut self) -> PrismaResult<()> {
        // Check if the queue is already running
        {
            let status = *self.status.read().unwrap();
            println!("Starting Standard queue with current status: {:?}", status);
            if status == QueueStatus::Running {
                println!("Standard queue is already running, returning early");
                return Ok(());
            }
            if status == QueueStatus::ShuttingDown {
                let err_msg = "Cannot start queue: Queue is shutting down";
                println!("{}", err_msg);
                return Err(GenericError::from(err_msg));
            }
        }

        info!("Starting Standard queue");
        println!("Starting Standard queue");

        // Create a new channel for the worker loop
        println!("Creating new channel with capacity {}", self.config.standard_queue_capacity);
        let (tx, mut rx) = mpsc::channel(self.config.standard_queue_capacity);

        // Store the sender in self.tx for future enqueues
        self.tx = tx;
        println!("New channel created");

        // Create worker
        println!("Creating worker loop");
        let worker_handle = create_worker_loop(
            rx,
            self.config.standard_strategy,
            self.stats.clone(),
            self.status.clone(),
            Some(self.task_timeout), // Use timeout for standard tasks
        );

        // Store the worker handle
        println!("Storing worker handle");
        self.worker_handles.push(worker_handle);
        println!("Worker handle stored");

        // Update status
        {
            println!("Updating queue status to Running");
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Running;
            println!("Queue status updated to Running");
        }

        // Wait a short time to ensure the worker loop has started
        println!("Waiting for worker loop to initialize");
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        println!("Worker loop initialization wait complete");

        println!("Standard queue started");
        info!("Standard queue started");
        Ok(())
    }

    async fn stop(&mut self) -> PrismaResult<()> {
        // Check if the queue is already stopped
        {
            let status = *self.status.read().unwrap();
            if status == QueueStatus::Stopped {
                return Ok(());
            }
        }

        info!("Stopping Standard queue");

        // Update status to shutting down
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::ShuttingDown;
        }

        // Abort all worker tasks
        for handle in self.worker_handles.drain(..) {
            handle.abort();
        }

        // Create a new channel
        let (tx, rx) = mpsc::channel(self.config.standard_queue_capacity);
        self.tx = tx;
        self.rx = Some(rx);

        // Update status to stopped
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Stopped;
        }

        info!("Standard queue stopped");
        Ok(())
    }

    async fn pause(&mut self) -> PrismaResult<()> {
        // Check if the queue is running
        {
            let status = *self.status.read().unwrap();
            if status != QueueStatus::Running {
                return Err(GenericError::from(
                    "Cannot pause queue: Queue is not running",
                ));
            }
        }

        info!("Pausing Standard queue");

        // Update status
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Paused;
        }

        info!("Standard queue paused");
        Ok(())
    }

    async fn resume(&mut self) -> PrismaResult<()> {
        // Check if the queue is paused
        {
            let status = *self.status.read().unwrap();
            if status != QueueStatus::Paused {
                return Err(GenericError::from(
                    "Cannot resume queue: Queue is not paused",
                ));
            }
        }

        info!("Resuming Standard queue");

        // Update status
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Running;
        }

        info!("Standard queue resumed");
        Ok(())
    }
}

#[async_trait]
impl StandardQueueTrait for StandardPriorityQueue {
    fn set_task_timeout(&mut self, timeout: Duration) -> PrismaResult<()> {
        // Check if the timeout is valid
        if timeout.as_millis() == 0 {
            return Err(GenericError::from(
                "Cannot set task timeout: Timeout must be greater than 0",
            ));
        }

        info!("Setting Standard queue task timeout to {:?}", timeout);

        // Update the timeout
        self.task_timeout = timeout;

        Ok(())
    }

    fn task_timeout(&self) -> Duration {
        self.task_timeout
    }
}