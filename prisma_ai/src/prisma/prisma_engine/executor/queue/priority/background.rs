// =================================================================================================
// File: /Users/<USER>/Documents/prisma_workspace/prisma_ai/src/prisma/prisma_engine/executor/queue/priority/background.rs
// =================================================================================================
// Purpose: Implements the BackgroundPriorityQueue for handling low-priority tasks. This queue is
// designed for tasks that are not time-sensitive and can be executed in the background.
//
// Integration:
// - `traits.rs`: Implements the PriorityQueueTrait and BackgroundQueueTrait defined there.
// - `types.rs`: Uses the types defined there.
// - `generics.rs`: Uses the utility functions defined there.
// - `Task`: Operates on Task objects.
// - `err.rs`: Uses PrismaResult for error handling.
// =================================================================================================

use async_trait::async_trait;
use std::any::Any;
use std::sync::{Arc, RwLock};
use std::time::{Duration, Instant};
use tokio::sync::{mpsc, Mutex, oneshot, Semaphore};
use tokio::task::JoinHandle;
use tracing::{debug, error, info, instrument, trace, warn};

use crate::err::{GenericError, PrismaResult};
use crate::prisma::prisma_engine::executor::queue::direct::CloneableTask;
use crate::prisma::prisma_engine::executor::queue::{RayonQueue, RayonQueueTrait};
use crate::prisma::prisma_engine::traits::Task;
use crate::prisma::prisma_engine::types::{ExecutionStrategyType, TaskId, TaskPriority};

use super::generics::create_worker_loop;
use super::traits::{BackgroundQueueTrait, CloneableTaskWrapper, PriorityQueueTrait};
use super::types::{PrioritizedTask, PriorityQueueConfig, PriorityQueueStats, QueueStatus};

/// A queue for handling low-priority background tasks.
///
/// This queue is designed for tasks that are not time-sensitive and can be
/// executed in the background with limited concurrency.
#[derive(Debug)]
pub struct BackgroundPriorityQueue {
    /// Configuration for the queue
    config: PriorityQueueConfig,

    /// Channel sender for enqueueing tasks
    tx: mpsc::Sender<PrioritizedTask>,

    /// Channel receiver for dequeueing tasks
    rx: Option<mpsc::Receiver<PrioritizedTask>>,

    /// Semaphore for limiting concurrent task execution
    concurrency_limiter: Arc<Semaphore>,

    /// Statistics about task execution
    stats: Arc<RwLock<PriorityQueueStats>>,

    /// Status of the queue
    status: Arc<RwLock<QueueStatus>>,

    /// Handles for the worker tasks
    worker_handles: Vec<JoinHandle<()>>,

    /// Reference to the RayonQueue for executing tasks
    /// This is optional because it's set after construction
    rayon_queue: Option<Arc<crate::prisma::prisma_engine::executor::queue::RayonQueue>>,
}

impl Clone for BackgroundPriorityQueue {
    fn clone(&self) -> Self {
        // Create a new channel with the same capacity
        let (tx, rx) = mpsc::channel(self.config.background_queue_capacity);

        BackgroundPriorityQueue {
            config: self.config.clone(),
            tx,
            rx: Some(rx),
            concurrency_limiter: self.concurrency_limiter.clone(),
            stats: self.stats.clone(),
            status: self.status.clone(),
            worker_handles: Vec::new(), // Empty vector for worker handles
            rayon_queue: self.rayon_queue.clone(), // Clone the reference to the RayonQueue
        }
    }
}

impl BackgroundPriorityQueue {
    /// Creates a new BackgroundPriorityQueue with the given configuration.
    pub fn new(config: PriorityQueueConfig) -> Self {
        let (tx, rx) = mpsc::channel(config.background_queue_capacity);

        BackgroundPriorityQueue {
            concurrency_limiter: Arc::new(Semaphore::new(config.background_concurrency_limit)),
            stats: Arc::new(RwLock::new(PriorityQueueStats::new())),
            status: Arc::new(RwLock::new(QueueStatus::Stopped)),
            worker_handles: Vec::new(),
            config,
            tx,
            rx: Some(rx),
            rayon_queue: None, // Initially None, will be set later
        }
    }

    /// Creates a new BackgroundPriorityQueue with default configuration.
    pub fn default() -> Self {
        Self::new(PriorityQueueConfig::default())
    }

    /// Sets the RayonQueue reference for this queue.
    pub fn set_rayon_queue(&mut self, rayon_queue: Arc<crate::prisma::prisma_engine::executor::queue::RayonQueue>) {
        self.rayon_queue = Some(rayon_queue);
    }

    /// Returns a clone of the current statistics.
    pub fn get_stats(&self) -> PriorityQueueStats {
        self.stats.read().unwrap().clone()
    }

    /// Returns the current status of the queue.
    pub fn get_status(&self) -> QueueStatus {
        *self.status.read().unwrap()
    }
}

#[async_trait]
impl PriorityQueueTrait for BackgroundPriorityQueue {
    fn priority_level(&self) -> Vec<TaskPriority> {
        vec![TaskPriority::Low]
    }

    fn execution_strategy(&self) -> ExecutionStrategyType {
        self.config.background_strategy
    }

    #[instrument(
        name = "background_queue_enqueue",
        skip(self, task),
        level = "debug"
    )]
    async fn enqueue(
        &self,
        task: Box<dyn Task>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
    )> {
        // Check if the queue is running
        let current_status = *self.status.read().unwrap();
        println!("Background queue enqueue called with status: {:?}", current_status);
        if current_status != QueueStatus::Running {
            let err_msg = format!("Cannot enqueue task: Background queue is not running (status: {:?})", current_status);
            println!("{}", err_msg);
            return Err(GenericError::from(err_msg));
        }

        let task_id = task.id();
        let priority = task.priority();

        // Verify that the task has the correct priority
        if priority != TaskPriority::Low {
            let warning_msg = format!("Task {} has priority {:?} but was submitted to Background queue", task_id, priority);
            println!("{}", warning_msg);
            warn!("{}", warning_msg);
        }

        println!("Enqueuing task {} in Background queue", task_id);
        debug!("Enqueuing task {} in Background queue", task_id);

        // Create a oneshot channel to receive the task result
        let (result_sender, result_receiver) = oneshot::channel();
        println!("Created oneshot channel for task {}", task_id);

        // Create a PrioritizedTask
        let prioritized_task = PrioritizedTask {
            priority,
            task_id,
            task: Arc::new(Mutex::new(task)),
            result_sender: Some(result_sender),
            enqueued_at: Instant::now(),
        };
        println!("Created PrioritizedTask for task {}", task_id);

        // Send the task to the channel
        println!("Sending task {} to Background queue channel", task_id);
        match tokio::time::timeout(
            std::time::Duration::from_secs(5), // 5 second timeout for sending
            self.tx.send(prioritized_task)
        ).await {
            Ok(send_result) => {
                if let Err(e) = send_result {
                    let err_msg = format!("Failed to enqueue task {}: {}", task_id, e);
                    println!("{}", err_msg);
                    error!("{}", err_msg);
                    return Err(GenericError::from(err_msg));
                }
                println!("Successfully sent task {} to Background queue channel", task_id);
            },
            Err(_) => {
                let err_msg = format!("Timeout while trying to enqueue task {} in Background queue", task_id);
                println!("{}", err_msg);
                error!("{}", err_msg);
                return Err(GenericError::from(err_msg));
            }
        }

        // Update queue length in stats
        {
            let mut stats = self.stats.write().unwrap();
            let current_length = stats.queue_length;
            println!("Updating Background queue length from {} to {}", current_length, current_length + 1);
            stats.update_queue_length(current_length + 1);
        }

        println!("Task {} enqueued successfully in Background queue", task_id);
        debug!("Task {} enqueued successfully in Background queue", task_id);
        Ok((task_id, result_receiver))
    }

    #[instrument(
        name = "background_queue_enqueue_arc",
        skip(self, task),
        level = "debug"
    )]
    async fn enqueue_arc<T: CloneableTask + 'static>(
        &self,
        task: Arc<T>,
    ) -> PrismaResult<(
        TaskId,
        oneshot::Receiver<PrismaResult<Box<dyn Any + Send>>>,
    )> {
        // Check if the queue is running
        if *self.status.read().unwrap() != QueueStatus::Running {
            return Err(GenericError::from(
                "Cannot enqueue task: Background queue is not running",
            ));
        }

        let task_id = task.id();
        let priority = task.priority();

        // Verify that the task has the correct priority
        if priority != TaskPriority::Low {
            warn!(
                "Task {} has priority {:?} but was submitted to Background queue",
                task_id, priority
            );
        }

        debug!("Enqueuing task {} in Background queue (Arc)", task_id);

        // Create a oneshot channel to receive the task result
        let (result_sender, result_receiver) = oneshot::channel();

        // Create a PrioritizedTask with the Arc<T> directly
        // We'll handle the task execution in the worker loop
        let prioritized_task = PrioritizedTask {
            priority,
            task_id,
            task: Arc::new(Mutex::new(Box::new(CloneableTaskWrapper(task)))),
            result_sender: Some(result_sender),
            enqueued_at: Instant::now(),
        };

        // Send the task to the channel
        if let Err(e) = self.tx.send(prioritized_task).await {
            let err_msg = format!("Failed to enqueue task {}: {}", task_id, e);
            error!("{}", err_msg);
            return Err(GenericError::from(err_msg));
        }

        // Update queue length in stats
        {
            let mut stats = self.stats.write().unwrap();
            let current_length = stats.queue_length;
            stats.update_queue_length(current_length + 1);
        }

        debug!("Task {} enqueued successfully in Background queue (Arc)", task_id);
        Ok((task_id, result_receiver))
    }

    fn queue_length(&self) -> usize {
        self.stats.read().unwrap().queue_length
    }

    fn is_full(&self) -> bool {
        self.queue_length() >= self.capacity()
    }

    fn capacity(&self) -> usize {
        self.config.background_queue_capacity
    }

    async fn start(&mut self) -> PrismaResult<()> {
        // Check if the queue is already running
        {
            let status = *self.status.read().unwrap();
            println!("Starting Background queue with current status: {:?}", status);
            if status == QueueStatus::Running {
                println!("Background queue is already running, returning early");
                return Ok(());
            }
            if status == QueueStatus::ShuttingDown {
                let err_msg = "Cannot start queue: Queue is shutting down";
                println!("{}", err_msg);
                return Err(GenericError::from(err_msg));
            }
        }

        println!("Starting Background queue");
        info!("Starting Background queue");

        // Check if we have a RayonQueue reference
        if self.rayon_queue.is_none() {
            let err_msg = "Cannot start Background queue: RayonQueue reference not set";
            println!("{}", err_msg);
            error!("{}", err_msg);
            return Err(GenericError::from(err_msg));
        }

        // Create a custom worker loop that transfers tasks to the RayonQueue
        println!("Creating custom worker loop that transfers tasks to the RayonQueue");
        let stats_clone = self.stats.clone();
        let status_clone = self.status.clone();

        // Clone the RayonQueue reference to move into the worker loop
        let rayon_queue_clone = match &self.rayon_queue {
            Some(queue) => queue.clone(),
            None => {
                let err_msg = "Cannot start Background queue: RayonQueue reference not set";
                println!("{}", err_msg);
                error!("{}", err_msg);
                return Err(GenericError::from(err_msg));
            }
        };

        // Create a new channel for the worker loop
        println!("Creating new channel with capacity {}", self.config.background_queue_capacity);
        let (tx, mut rx) = mpsc::channel(self.config.background_queue_capacity);

        // Store the sender in self.tx for future enqueues
        self.tx = tx;

        // We'll use rx directly in the worker loop
        // No need to store it in self.rx
        println!("New channel created");

        let worker_handle = tokio::spawn(async move {
            info!("Background queue worker loop started");
            println!("Background queue worker loop started");

            // Add a timeout for receiving tasks to prevent blocking indefinitely
            let receive_timeout = Duration::from_secs(1);

            // Add a debug ping to check if the loop is still alive
            let ping_interval = tokio::time::Duration::from_secs(5);
            let ping_task = tokio::spawn(async move {
                let mut interval = tokio::time::interval(ping_interval);
                loop {
                    interval.tick().await;
                    debug!("Background queue worker loop ping");
                    println!("Background queue worker loop ping");
                }
            });

            loop {
                // Check if the queue is shutting down or stopped before trying to receive
                {
                    let queue_status = *status_clone.read().unwrap();
                    if queue_status == QueueStatus::ShuttingDown || queue_status == QueueStatus::Stopped {
                        debug!("Background queue worker loop exiting due to queue status: {:?}", queue_status);
                        println!("Background queue worker loop exiting due to queue status: {:?}", queue_status);
                        break;
                    }

                    if queue_status == QueueStatus::Paused {
                        // If paused, wait for a short time and check again
                        tokio::time::sleep(Duration::from_millis(100)).await;
                        continue;
                    }
                }

                // Try to receive a task with a timeout
                match tokio::time::timeout(receive_timeout, rx.recv()).await {
                    Ok(Some(prioritized_task)) => {
                        let task_id = prioritized_task.task_id;
                        let task_arc = prioritized_task.task;
                        let result_sender = prioritized_task.result_sender;

                        println!("Background queue worker received task {}", task_id);
                        debug!("Background queue worker received task {}", task_id);

                        // Update queue length in stats (decrement since we're processing a task)
                        {
                            let mut stats_guard = stats_clone.write().unwrap();
                            let current_length = stats_guard.queue_length;
                            if current_length > 0 {
                                println!("Updating queue length from {} to {}", current_length, current_length - 1);
                                stats_guard.update_queue_length(current_length - 1);
                            }
                        }

                        // Instead of executing the task directly, transfer it to the RayonQueue
                        println!("Transferring task {} to RayonQueue", task_id);

                        // We need to convert the PrioritizedTask to a format that RayonQueue can accept
                        // First, extract the task from the Arc<Mutex<Box<dyn Task>>>
                        let task = {
                            let mut task_guard = task_arc.lock().await;
                            let task_ref: &mut dyn Task = &mut **task_guard;

                            // Clone the task to transfer ownership
                            task_ref.clone_box()
                        };

                        // Check the RayonQueue status
                        let rayon_status = RayonQueue::get_status_from_arc(&rayon_queue_clone);
                        println!("RayonQueue status before enqueuing task {}: {:?}", task_id, rayon_status);

                        // Now enqueue the task in the RayonQueue
                        match rayon_queue_clone.enqueue(task).await {
                            Ok((_, receiver)) => {
                                println!("Task {} successfully transferred to RayonQueue", task_id);

                                // Forward the result from the RayonQueue to the original sender
                                tokio::spawn(async move {
                                    match receiver.await {
                                        Ok(result) => {
                                            println!("Received result from RayonQueue for task {}", task_id);
                                            if let Some(sender) = result_sender {
                                                if sender.send(result).is_err() {
                                                    error!("Failed to forward result for task {}: receiver dropped", task_id);
                                                    println!("Failed to forward result for task {}: receiver dropped", task_id);
                                                } else {
                                                    println!("Successfully forwarded result for task {}", task_id);
                                                }
                                            }
                                        },
                                        Err(e) => {
                                            error!("Failed to receive result from RayonQueue for task {}: {}", task_id, e);
                                            println!("Failed to receive result from RayonQueue for task {}: {}", task_id, e);

                                            // Send the error back to the original sender
                                            if let Some(sender) = result_sender {
                                                let err_msg = format!("Failed to receive result from RayonQueue: {}", e);
                                                if sender.send(Err(GenericError::from(err_msg))).is_err() {
                                                    error!("Failed to send error result for task {}: receiver dropped", task_id);
                                                    println!("Failed to send error result for task {}: receiver dropped", task_id);
                                                }
                                            }
                                        }
                                    }
                                });
                            },
                            Err(e) => {
                                error!("Failed to enqueue task {} in RayonQueue: {}", task_id, e);
                                println!("Failed to enqueue task {} in RayonQueue: {}", task_id, e);

                                // Send the error back to the original sender
                                if let Some(sender) = result_sender {
                                    let err_msg = format!("Failed to enqueue task in RayonQueue: {}", e);
                                    if sender.send(Err(GenericError::from(err_msg))).is_err() {
                                        error!("Failed to send error result for task {}: receiver dropped", task_id);
                                        println!("Failed to send error result for task {}: receiver dropped", task_id);
                                    }
                                }
                            }
                        }
                    },
                    Ok(None) => {
                        // Channel closed, exit the loop
                        debug!("Background queue worker loop exiting due to closed channel");
                        println!("Background queue worker loop exiting due to closed channel");
                        break;
                    },
                    Err(_) => {
                        // Timeout occurred, check status again
                        trace!("Timeout waiting for task, checking status again");
                        println!("Timeout waiting for task, checking status again");
                        continue;
                    }
                }
            }

            // Cancel the ping task
            ping_task.abort();

            info!("Background queue worker loop stopped");
            println!("Background queue worker loop stopped");
        });

        println!("Custom worker loop created");

        // Store the worker handle
        println!("Storing worker handle");
        self.worker_handles.push(worker_handle);
        println!("Worker handle stored");

        // Update status
        {
            println!("Updating queue status to Running");
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Running;
            println!("Queue status updated to Running");
        }

        // Wait a short time to ensure the worker loop has started
        println!("Waiting for worker loop to initialize");
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        println!("Worker loop initialization wait complete");

        println!("Background queue started");
        info!("Background queue started");
        Ok(())
    }

    async fn stop(&mut self) -> PrismaResult<()> {
        // Check if the queue is already stopped
        {
            let status = *self.status.read().unwrap();
            if status == QueueStatus::Stopped {
                return Ok(());
            }
        }

        info!("Stopping Background queue");

        // Update status to shutting down
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::ShuttingDown;
        }

        // Abort all worker tasks
        for handle in self.worker_handles.drain(..) {
            handle.abort();
        }

        // Create a new channel
        let (tx, rx) = mpsc::channel(self.config.background_queue_capacity);
        self.tx = tx;
        self.rx = Some(rx);

        // Update status to stopped
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Stopped;
        }

        info!("Background queue stopped");
        Ok(())
    }

    async fn pause(&mut self) -> PrismaResult<()> {
        // Check if the queue is running
        {
            let status = *self.status.read().unwrap();
            if status != QueueStatus::Running {
                return Err(GenericError::from(
                    "Cannot pause queue: Queue is not running",
                ));
            }
        }

        info!("Pausing Background queue");

        // Update status
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Paused;
        }

        info!("Background queue paused");
        Ok(())
    }

    async fn resume(&mut self) -> PrismaResult<()> {
        // Check if the queue is paused
        {
            let status = *self.status.read().unwrap();
            if status != QueueStatus::Paused {
                return Err(GenericError::from(
                    "Cannot resume queue: Queue is not paused",
                ));
            }
        }

        info!("Resuming Background queue");

        // Update status
        {
            let mut status = self.status.write().unwrap();
            *status = QueueStatus::Running;
        }

        info!("Background queue resumed");
        Ok(())
    }
}

#[async_trait]
impl BackgroundQueueTrait for BackgroundPriorityQueue {
    fn set_concurrency_limit(&mut self, limit: usize) -> PrismaResult<()> {
        // Check if the limit is valid
        if limit == 0 {
            return Err(GenericError::from(
                "Cannot set concurrency limit: Limit must be greater than 0",
            ));
        }

        info!("Setting Background queue concurrency limit to {}", limit);

        // Create a new semaphore with the new limit
        self.concurrency_limiter = Arc::new(Semaphore::new(limit));

        Ok(())
    }

    fn concurrency_limit(&self) -> usize {
        self.concurrency_limiter.available_permits()
    }
}