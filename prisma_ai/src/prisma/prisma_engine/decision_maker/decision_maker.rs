// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/decision_maker.rs
// =================================================================================================
// Purpose: Implements the main decision-making logic for the PrismaEngine. This file contains
// the RuleBasedDecisionMaker struct that implements the DecisionLogic trait, providing the core
// functionality for determining the optimal execution strategy for tasks based on their resource
// requirements and current system resource availability.
//
// Integration:
// - Internal Dependencies:
//   - decision_maker/mod.rs: Exports this module
//   - decision_maker/types.rs: Uses DecisionMakerConfig and other types
//   - decision_maker/rules.rs: May use rule definitions
//   - decision_maker/state.rs: May use state tracking
//   - prisma_scores: Uses task resource requirement evaluation
//   - system_scores: Uses system resource availability evaluation
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses core engine types
//   - prisma_engine/traits.rs: Implements the DecisionLogic trait
//   - err: Uses PrismaResult for error handling
//   - tracing: For logging and debugging
//
// - Module Interactions:
//   - Receives task information from PrismaEngine
//   - Receives system information from Monitor module via PrismaEngine
//   - Provides execution strategy decisions to PrismaEngine
//   - Influences task routing in Executor module
//
// Platform Considerations:
// - This module is platform-independent as it makes decisions based on abstract scores
// =================================================================================================

use async_trait::async_trait;
use tracing::{debug, warn, info};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

// Use crate-level error types and engine types/traits
use crate::err::PrismaResult;
use crate::prisma::prisma_engine::types::{
    PrismaScore, SystemScore, ExecutionStrategyType, TaskCategory, TaskPriority, ResourceType
};
use crate::prisma::prisma_engine::traits::DecisionLogic; // Import the main trait
use crate::prisma::prisma_engine::monitor::system::traits::SystemMonitoring;

// Import decision_maker specific types and logic
use super::types::{DecisionMakerConfig, StateTrackerConfig, DecisionReason};
use super::prisma_scores::{
    create_score_evaluator,
    ScoreEvaluator,
    PrismaScoreConfig
};
use super::system_scores::{
    SystemScoreEvaluator,
    create_system_score_evaluator,
    create_system_score_evaluator_with_config,
    SystemScoreConfig,
    DetailedSystemScore,
    is_system_critical,
    get_most_constrained_resource,
    ResourceMetrics
};
use super::rules::{create_rule_set, create_empty_rule_set};
use super::state::{create_state_tracker, create_state_tracker_with_config};
use super::traits::{RuleSet, StateTracker, StrategySelector};

// A rule-based implementation of the DecisionLogic trait
// We can't derive Debug because ScoreEvaluator and SystemScoreEvaluator don't implement Debug
pub struct RuleBasedDecisionMaker {
    /// Configuration for the decision maker
    config: DecisionMakerConfig,
    /// Evaluator for task resource requirements (PrismaScore)
    score_evaluator: Arc<dyn ScoreEvaluator>,
    /// Evaluator for system resource availability (SystemScore)
    system_score_evaluator: Option<Arc<RwLock<Box<dyn SystemScoreEvaluator>>>>,
    /// Rule set for decision making
    rule_set: Option<Arc<RwLock<Box<dyn RuleSet>>>>,
    /// State tracker for LLM models
    state_tracker: Option<Arc<RwLock<Box<dyn StateTracker>>>>,
}

// Implement Debug manually
impl std::fmt::Debug for RuleBasedDecisionMaker {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("RuleBasedDecisionMaker")
            .field("config", &self.config)
            .field("score_evaluator", &"<dyn ScoreEvaluator>")
            .field("system_score_evaluator", &"<Option<dyn SystemScoreEvaluator>>")
            .field("rule_set", &"<Option<dyn RuleSet>>")
            .field("state_tracker", &"<Option<dyn StateTracker>>")
            .finish()
    }
}

impl RuleBasedDecisionMaker {
    /// Create a new RuleBasedDecisionMaker with default settings
    pub fn new(config: DecisionMakerConfig) -> Self {
        // Create a PrismaScoreConfig with default values
        let prisma_config = PrismaScoreConfig::default();

        // Create a score evaluator for task resource requirements
        let score_evaluator = create_score_evaluator(Some(prisma_config));

        // Create a default rule set
        let rule_set = create_rule_set();
        let rule_set = Some(Arc::new(RwLock::new(rule_set)));

        // Create a default state tracker
        let state_tracker = create_state_tracker();
        let state_tracker = Some(Arc::new(RwLock::new(state_tracker)));

        // System score evaluator is initialized later with set_system_monitor
        RuleBasedDecisionMaker {
            config,
            score_evaluator,
            system_score_evaluator: None,
            rule_set,
            state_tracker
        }
    }

    /// Set the system monitor for system resource availability evaluation
    pub fn with_system_monitor(
        config: DecisionMakerConfig,
        system_monitor: Arc<RwLock<dyn SystemMonitoring>>
    ) -> Self {
        // Create a PrismaScoreConfig with default values
        let prisma_config = PrismaScoreConfig::default();

        // Create a score evaluator for task resource requirements
        let score_evaluator = create_score_evaluator(Some(prisma_config));

        // Create a system score evaluator with default settings
        let system_score_evaluator = create_system_score_evaluator(system_monitor);
        let system_score_evaluator = Some(Arc::new(RwLock::new(system_score_evaluator)));

        // Create a default rule set
        let rule_set = create_rule_set();
        let rule_set = Some(Arc::new(RwLock::new(rule_set)));

        // Create a default state tracker
        let state_tracker = create_state_tracker();
        let state_tracker = Some(Arc::new(RwLock::new(state_tracker)));

        RuleBasedDecisionMaker {
            config,
            score_evaluator,
            system_score_evaluator,
            rule_set,
            state_tracker
        }
    }

    /// Set the system monitor for system resource availability evaluation with custom config
    pub fn with_system_monitor_and_config(
        config: DecisionMakerConfig,
        system_monitor: Arc<RwLock<dyn SystemMonitoring>>,
        system_score_config: SystemScoreConfig
    ) -> Self {
        // Create a PrismaScoreConfig with default values
        let prisma_config = PrismaScoreConfig::default();

        // Create a score evaluator for task resource requirements
        let score_evaluator = create_score_evaluator(Some(prisma_config));

        // Create a system score evaluator with custom settings
        let system_score_evaluator = create_system_score_evaluator_with_config(system_monitor, system_score_config);
        let system_score_evaluator = Some(Arc::new(RwLock::new(system_score_evaluator)));

        // Create a default rule set
        let rule_set = create_rule_set();
        let rule_set = Some(Arc::new(RwLock::new(rule_set)));

        // Create a default state tracker
        let state_tracker = create_state_tracker();
        let state_tracker = Some(Arc::new(RwLock::new(state_tracker)));

        RuleBasedDecisionMaker {
            config,
            score_evaluator,
            system_score_evaluator,
            rule_set,
            state_tracker
        }
    }

    /// Set the system monitor after initialization
    pub async fn set_system_monitor(
        &mut self,
        system_monitor: Arc<RwLock<dyn SystemMonitoring>>
    ) -> PrismaResult<()> {
        // Create a system score evaluator with default settings
        let system_score_evaluator = create_system_score_evaluator(system_monitor);

        // Store the system score evaluator
        self.system_score_evaluator = Some(Arc::new(RwLock::new(system_score_evaluator)));

        Ok(())
    }

    /// Set the rule set after initialization
    pub async fn set_rule_set(
        &mut self,
        rule_set: Box<dyn RuleSet>
    ) -> PrismaResult<()> {
        // Store the rule set
        self.rule_set = Some(Arc::new(RwLock::new(rule_set)));

        Ok(())
    }

    /// Set the state tracker after initialization
    pub async fn set_state_tracker(
        &mut self,
        state_tracker: Box<dyn StateTracker>
    ) -> PrismaResult<()> {
        // Store the state tracker
        self.state_tracker = Some(Arc::new(RwLock::new(state_tracker)));

        Ok(())
    }

    /// Set the state tracker with custom configuration
    pub async fn set_state_tracker_with_config(
        &mut self,
        config: StateTrackerConfig
    ) -> PrismaResult<()> {
        // Create a state tracker with custom configuration
        let state_tracker = create_state_tracker_with_config(config);

        // Store the state tracker
        self.state_tracker = Some(Arc::new(RwLock::new(state_tracker)));

        Ok(())
    }

    /// Helper function to check resource availability against task requirements
    async fn check_resource_constraints(
        &self,
        task_score: &PrismaScore,
        system_score: &SystemScore
    ) -> bool {
        // Convert SystemScore to HashMap<ResourceType, ResourceUsage>
        let mut available_resources = HashMap::new();
        for (resource, usage) in &system_score.availability {
            available_resources.insert(*resource, *usage);
        }

        // Use the score evaluator to check constraints
        match self.score_evaluator.check_resource_constraints(task_score, &available_resources).await {
            Ok(result) => result,
            Err(e) => {
                warn!("Error checking resource constraints: {:?}", e);
                // Default to true if there's an error to avoid blocking tasks
                true
            }
        }
    }

    /// Get the current system score from the system score evaluator
    async fn get_current_system_score(&self) -> Option<DetailedSystemScore> {
        if let Some(evaluator) = &self.system_score_evaluator {
            match evaluator.read().await.get_current_score().await {
                Ok(score) => Some(score),
                Err(e) => {
                    warn!("Error getting current system score: {:?}", e);
                    None
                }
            }
        } else {
            None
        }
    }

    /// Check if the system is in a critical state
    async fn is_system_critical(&self) -> bool {
        if let Some(score) = self.get_current_system_score().await {
            // Use the system_scores module function to check if the system is critical
            is_system_critical(&score, &SystemScoreConfig::default())
        } else {
            // If we can't get the system score, assume it's not critical
            false
        }
    }

    /// Get the most constrained resource in the system
    async fn get_most_constrained_resource(&self) -> Option<ResourceType> {
        if let Some(score) = self.get_current_system_score().await {
            // Use the system_scores module function to get the most constrained resource
            get_most_constrained_resource(&score)
        } else {
            // If we can't get the system score, return None
            None
        }
    }

    /// Get a model's state from the state tracker
    pub async fn get_model_state(&self, model_id: &str) -> PrismaResult<Option<super::types::ModelState>> {
        if let Some(state_tracker) = &self.state_tracker {
            match state_tracker.read().await.get_model_state(model_id).await {
                Ok(state) => Ok(Some(state)),
                Err(e) => {
                    warn!("Error getting model state for {}: {:?}", model_id, e);
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }

    /// Check if a model is available
    pub async fn is_model_available(&self, model_id: &str) -> bool {
        if let Some(state_tracker) = &self.state_tracker {
            match state_tracker.read().await.is_model_available(model_id).await {
                Ok(available) => available,
                Err(e) => {
                    warn!("Error checking model availability for {}: {:?}", model_id, e);
                    false
                }
            }
        } else {
            false
        }
    }

    /// Get all model states
    pub async fn get_all_model_states(&self) -> PrismaResult<HashMap<String, super::types::ModelState>> {
        if let Some(state_tracker) = &self.state_tracker {
            state_tracker.read().await.get_all_model_states().await
        } else {
            Ok(HashMap::new())
        }
    }

    /// Add a rule to the rule set
    pub async fn add_rule(&self, rule: Box<dyn super::traits::Rule>) -> PrismaResult<()> {
        if let Some(rule_set) = &self.rule_set {
            rule_set.write().await.add_rule(rule);
            Ok(())
        } else {
            Err(crate::err::types::errors_mod::PrismaError::Generic("No rule set available".to_string()).into())
        }
    }

    /// Get all rules in the rule set
    pub async fn get_rules(&self) -> PrismaResult<Vec<String>> {
        if let Some(rule_set) = &self.rule_set {
            let rule_set_guard = rule_set.read().await;
            let rules = rule_set_guard.get_rules();
            let rule_names = rules.iter().map(|r| r.name().to_string()).collect();
            Ok(rule_names)
        } else {
            Ok(Vec::new())
        }
    }

    /// Enable or disable a rule
    pub async fn set_rule_enabled(&self, rule_name: &str, enabled: bool) -> PrismaResult<()> {
        if let Some(rule_set) = &self.rule_set {
            let mut rule_set = rule_set.write().await;
            if let Some(rule) = rule_set.get_rule_mut(rule_name) {
                rule.set_enabled(enabled);
                Ok(())
            } else {
                Err(crate::err::types::errors_mod::PrismaError::Generic(format!("Rule {} not found", rule_name)).into())
            }
        } else {
            Err(crate::err::types::errors_mod::PrismaError::Generic("No rule set available".to_string()).into())
        }
    }

    /// Start the state tracker
    pub async fn start_state_tracker(&self) -> PrismaResult<()> {
        if let Some(_state_tracker) = &self.state_tracker {
            // We can't directly access the start method because it's not part of the StateTracker trait
            // Instead, we'll create a new state tracker with the same configuration
            // This is a workaround until we modify the StateTracker trait to include start/stop methods

            // For now, just log that we would start the state tracker
            info!("State tracker would be started (implementation pending)");
            Ok(())
        } else {
            warn!("No state tracker available");
            Ok(()) // Not an error, just no-op
        }
    }

    /// Update the configuration
    pub fn update_config(&mut self, config: DecisionMakerConfig) {
        self.config = config;
    }

    /// Get the current configuration
    pub fn get_config(&self) -> &DecisionMakerConfig {
        &self.config
    }

    /// Create a new rule set with the given rules
    pub async fn create_new_rule_set(&mut self, rules: Vec<Box<dyn super::traits::Rule>>) -> PrismaResult<()> {
        // Create a new empty rule set
        let mut rule_set = create_empty_rule_set();

        // Add all rules
        for rule in rules {
            rule_set.add_rule(rule);
        }

        // Set the rule set
        self.rule_set = Some(Arc::new(RwLock::new(rule_set)));

        Ok(())
    }

    /// Extract the model ID from a task score
    fn extract_model_id_from_task(&self, task_score: &PrismaScore) -> String {
        // In a real implementation, we would extract the model ID from the task score
        // For now, we'll use a default model ID

        // Check if there's any metadata in the resources
        for (resource, requirement) in &task_score.resources {
            // LLM models often have specific GPU or memory requirements
            if *resource == ResourceType::GPU || *resource == ResourceType::Memory {
                // In a real implementation, we would extract model information from the requirement
                debug!("Found resource requirement for {:?}: {}", resource, requirement.0);
            }
        }

        // If we can't find a model ID, use a default
        // In a production system, this should be configured or determined from context
        "default_model".to_string()
    }
}

#[async_trait]
impl StrategySelector for RuleBasedDecisionMaker {
    async fn select_strategy(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<(ExecutionStrategyType, DecisionReason)> {
        // Use the decide_strategy method to get the strategy
        let strategy = self.decide_strategy(task_category, task_score, system_score, task_priority).await?;

        // Create a simple reason for the decision
        let reason = DecisionReason {
            description: format!("Strategy {:?} selected for task category {:?}", strategy, task_category),
            details: None,
            resource: None,
            resource_usage: None,
        };

        Ok((strategy, reason))
    }

    async fn can_execute(
        &self,
        task_score: &PrismaScore,
        system_score: &SystemScore,
    ) -> PrismaResult<bool> {
        // Check resource constraints
        let constraints_met = self.check_resource_constraints(task_score, system_score).await;

        // Check system critical state
        let system_critical = self.is_system_critical().await;

        // Can execute if constraints are met and system is not critical
        Ok(constraints_met && !system_critical)
    }

    fn get_recommended_strategy(&self, task_category: &TaskCategory) -> ExecutionStrategyType {
        // Get the recommended strategy from the config
        self.config.category_strategies
            .get(task_category)
            .copied()
            .unwrap_or(self.config.default_strategy)
    }

    fn set_recommended_strategy(&mut self, task_category: &TaskCategory, strategy: ExecutionStrategyType) {
        // Update the recommended strategy in the config
        self.config.category_strategies.insert(task_category.clone(), strategy);
    }
}

#[async_trait]
impl DecisionLogic for RuleBasedDecisionMaker {
    async fn decide_strategy(
        &self,
        task_category: &TaskCategory,
        task_score: &PrismaScore,
        system_score: &SystemScore,
        task_priority: TaskPriority,
    ) -> PrismaResult<ExecutionStrategyType> {

        debug!("Deciding strategy for task category: {:?}, priority: {:?}", task_category, task_priority);
        debug!("Task Score: {:?}", task_score);
        debug!("System Score: {:?}", system_score);

        // --- Rule-Based Decision Making ---
        if self.config.enable_rule_based_decisions {
            if let Some(rule_set) = &self.rule_set {
                // Evaluate all rules in the rule set
                let rule_results = match rule_set.read().await.evaluate_rules(
                    task_category,
                    task_score,
                    system_score,
                    task_priority
                ).await {
                    Ok(results) => results,
                    Err(e) => {
                        warn!("Error evaluating rules: {:?}. Falling back to direct decision making.", e);
                        Vec::new()
                    }
                };

                // Find the first triggered rule with a recommended strategy
                let triggered_rules: Vec<_> = rule_results.iter()
                    .filter(|r| r.triggered && r.recommended_strategy.is_some())
                    .collect();

                if !triggered_rules.is_empty() {
                    // Use the first triggered rule's recommendation
                    let rule = &triggered_rules[0];
                    let strategy = rule.recommended_strategy.unwrap();

                    debug!("Strategy decided by rule {}: {:?} -> {:?}",
                           rule.rule_name, task_category, strategy);
                    debug!("Reason: {}", rule.reason.description);

                    return Ok(strategy);
                }

                debug!("No rules triggered with strategy recommendations. Falling back to direct decision making.");
            } else {
                debug!("No rule set available. Falling back to direct decision making.");
            }
        }

        // --- Direct Decision Making (Fallback) ---

        // 1. Priority Override: Realtime tasks always use Tokio (or a dedicated strategy)
        if task_priority == TaskPriority::Realtime {
            debug!("Strategy decided by priority: Realtime -> Tokio");
            return Ok(ExecutionStrategyType::Tokio);
        }

        // 2. System Critical Check (Using SystemScoreEvaluator)
        let system_critical = self.is_system_critical().await;
        if system_critical {
            // If the system is in a critical state, we might want to use a less intensive strategy
            if let Some(resource) = self.get_most_constrained_resource().await {
                warn!("System resource critical: {:?}. Adjusting strategy.", resource);

                // Adjust strategy based on the constrained resource
                match resource {
                    ResourceType::CPU => {
                        // If CPU is constrained, avoid Rayon (parallel) execution
                        if matches!(task_category, TaskCategory::EmbeddingGeneration | TaskCategory::FileProcessing) {
                            debug!("CPU-constrained system: Using Tokio instead of Rayon");
                            return Ok(ExecutionStrategyType::Tokio);
                        }
                    },
                    ResourceType::Memory => {
                        // If memory is constrained, prefer Direct execution for simple tasks
                        if matches!(task_category, TaskCategory::Internal) {
                            debug!("Memory-constrained system: Using Direct execution");
                            return Ok(ExecutionStrategyType::Direct);
                        }
                    },
                    _ => {
                        // For other resources, proceed with normal decision making
                        debug!("Resource {:?} constrained, but no specific strategy adjustment", resource);
                    }
                }
            }
        }

        // 3. Resource Constraint Check (Using PrismaScoreEvaluator)
        let constraints_met = self.check_resource_constraints(task_score, system_score).await;
        if !constraints_met {
            // If constraints are NOT met, maybe force a less intensive strategy or delay
            warn!("Resource constraints potentially hit, adjusting strategy if possible.");

            // For CPU-intensive tasks, switch to Tokio if constraints aren't met
            if matches!(task_category, TaskCategory::EmbeddingGeneration | TaskCategory::FileProcessing) {
                debug!("Resource constraints hit: Using Tokio instead of Rayon");
                return Ok(ExecutionStrategyType::Tokio);
            }
        }

        // 4. Model State Check (Using StateTracker)
        if self.config.enable_state_tracking {
            if let Some(state_tracker) = &self.state_tracker {
                // Check if the task is LLM inference
                if matches!(task_category, TaskCategory::LLMInference) {
                    // Extract the model ID from task_score
                    let model_id = self.extract_model_id_from_task(task_score);

                    debug!("Checking availability of model: {}", model_id);

                    // Check if the model is available
                    match state_tracker.read().await.is_model_available(&model_id).await {
                        Ok(available) => {
                            if !available {
                                debug!("Model {} is not available. Using Tokio for loading.", model_id);
                                return Ok(ExecutionStrategyType::Tokio);
                            } else {
                                debug!("Model {} is available for use.", model_id);
                            }
                        },
                        Err(e) => {
                            warn!("Error checking model availability: {:?}. Proceeding with default strategy.", e);
                        }
                    }

                    // Check model memory requirements against system memory
                    if let Ok(model_memory) = state_tracker.read().await.get_model_memory_usage(&model_id).await {
                        // Get available system memory
                        let available_memory = system_score.availability
                            .get(&ResourceType::Memory)
                            .map(|usage| usage.0)
                            .unwrap_or(100.0);

                        // Convert percentage to bytes (rough estimate)
                        let system_memory = if let Some(score) = self.get_current_system_score().await {
                            // Get memory metrics from resource scores
                            if let Some(memory_score) = score.resource_scores.get(&ResourceType::Memory) {
                                if let Some(ResourceMetrics::Memory(memory_metrics)) = &memory_score.raw_metrics {
                                    memory_metrics.total_bytes
                                } else {
                                    // Default to a reasonable value if memory metrics are not available
                                    16 * 1024 * 1024 * 1024 // 16 GB
                                }
                            } else {
                                // Default to a reasonable value if memory score is not available
                                16 * 1024 * 1024 * 1024 // 16 GB
                            }
                        } else {
                            // Default to a reasonable value if we can't get the system score
                            16 * 1024 * 1024 * 1024 // 16 GB
                        };

                        let available_bytes = (system_memory as f64 * available_memory / 100.0) as u64;

                        // Check if we have enough memory
                        if model_memory > available_bytes {
                            warn!("Model {} requires {}MB but only {}MB available. Using Tokio for better memory management.",
                                  model_id, model_memory / (1024 * 1024), available_bytes / (1024 * 1024));
                            return Ok(ExecutionStrategyType::Tokio);
                        }
                    }
                }
            }
        }

        // 5. Category-Based Strategy Selection (Default)
        let strategy = match task_category {
            // CPU-bound tasks -> Rayon
            TaskCategory::EmbeddingGeneration | TaskCategory::FileProcessing => {
                ExecutionStrategyType::Rayon
            }
            // IO-bound or potentially blocking tasks -> Tokio
            TaskCategory::LLMInference | // Can be IO (GPU) or CPU bound, Tokio handles both well
            TaskCategory::DatabaseQuery |
            TaskCategory::NetworkRequest |
            TaskCategory::UICallback => {
                ExecutionStrategyType::Tokio
            }
            // Simple internal tasks -> Direct
            TaskCategory::Internal => {
                ExecutionStrategyType::Direct
            }
            // Default or Custom
            TaskCategory::Custom(_) => {
                // Check if we have a recommended strategy in the config
                self.config.category_strategies
                    .get(task_category)
                    .copied()
                    .unwrap_or(self.config.default_strategy)
            }
        };

        debug!("Strategy decided by category: {:?} -> {:?}", task_category, strategy);
        Ok(strategy)
    }
}
