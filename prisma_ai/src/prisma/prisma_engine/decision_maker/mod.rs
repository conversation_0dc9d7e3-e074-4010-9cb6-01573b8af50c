// =================================================================================================
// File: prisma_ai/src/prisma/prisma_engine/decision_maker/mod.rs
// =================================================================================================
// Purpose: Serves as the main entry point for the decision_maker module, which is responsible for
// determining the optimal execution strategy for tasks based on their resource requirements and
// current system resource availability. This module coordinates the evaluation of PrismaScores
// (task requirements) and SystemScores (resource availability) to make intelligent decisions
// about how tasks should be executed.
//
// Integration:
// - Internal Dependencies:
//   - prisma_scores: Evaluates task resource requirements
//   - system_scores: Evaluates system resource availability
//   - rules.rs: Defines decision-making rules
//   - state.rs: Tracks LLM state information
//   - types.rs: Defines decision maker types
//   - traits.rs: Defines decision maker traits
//   - generics.rs: Provides generic utilities
//   - decision_maker.rs: Contains the main RuleBasedDecisionMaker implementation
//
// - External Dependencies:
//   - prisma_engine/types.rs: Uses core engine types
//   - prisma_engine/traits.rs: Implements the DecisionLogic trait
//   - prisma_engine/tcl: Receives task information
//   - prisma_engine/monitor: Receives system information
//   - prisma_engine/executor: Provides execution context
//
// - Module Interactions:
//   - Receives task information from TCL module
//   - Receives system information from Monitor module
//   - Provides execution strategy decisions to PrismaEngine
//   - Influences task routing in Executor module
//
// Platform Considerations:
// - This module is platform-independent as it makes decisions based on abstract scores
// =================================================================================================

// Declare submodules for different aspects of decision making
pub mod prisma_scores;
pub mod system_scores;

// Declare other files in the decision_maker module
pub mod types;
pub mod traits;
pub mod generics;
pub mod rules;
pub mod state;
pub mod decision_maker; // Contains the main RuleBasedDecisionMaker struct

// Re-export the main DecisionMaker struct and its config
pub use decision_maker::RuleBasedDecisionMaker;
pub use types::DecisionMakerConfig;

// Re-export key components from prisma_scores
pub use prisma_scores::{
    PrismaScoreEvaluator,
    ScoreEvaluator,
    TaskScoreAnalyzer,
    QueueScoreAnalyzer,
    UsageScoreAnalyzer,
    PrismaScoreConfig,
    create_score_evaluator
};

// Re-export key components from system_scores
pub use system_scores::{
    // Main evaluator and factory functions
    SystemScoreEvaluator,
    create_system_score_evaluator,
    create_system_score_evaluator_with_config,

    // Resource-specific calculators
    ResourceScoreCalculator,
    CpuScoreCalculator,
    MemoryScoreCalculator,
    DiskScoreCalculator,
    NetworkScoreCalculator,

    // Core evaluation functions
    evaluate_system_score,
    calculate_trends,
    predict_future_score,
    is_system_critical,
    get_most_constrained_resource,
    calculate_system_health,

    // Types
    SystemScoreConfig,
    DetailedSystemScore,
    ResourceScore,
    ResourceTrend,
    ResourceThreshold,
    AvailabilityLevel
};
